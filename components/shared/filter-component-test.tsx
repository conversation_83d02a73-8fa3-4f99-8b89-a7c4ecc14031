import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { FilterComponent, FilterField, FilterValues } from "./filter-component";

export const FilterComponentTest = () => {
  const [filterValues, setFilterValues] = useState<FilterValues>({
    category: [],
    facility: "",
    onlyAvailable: false,
  });

  const filterFields: FilterField[] = [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Yoga", value: "yoga" },
        { label: "Pilates", value: "pilates" },
        { label: "Cardio", value: "cardio" },
        { label: "Strength", value: "strength" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      options: [
        { label: "Main Gym", value: "main-gym" },
        { label: "Pool Area", value: "pool" },
        { label: "Studio A", value: "studio-a" },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
      description: "Filter to show only available slots",
    },
  ];

  const handleFilterChange = (values: FilterValues) => {
    setFilterValues(values);
  };

  const handleApply = (values: FilterValues) => {
    console.log("Applied filters:", values);
  };

  const handleReset = () => {
    setFilterValues({
      category: [],
      facility: "",
      onlyAvailable: false,
    });
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1 p-4" space="lg">
        <Text className="text-2xl font-dm-sans-bold text-typography-900">
          Filter Component Test
        </Text>

        <FilterComponent
          fields={filterFields}
          values={filterValues}
          onValuesChange={handleFilterChange}
          onApply={handleApply}
          onReset={handleReset}
          title="Test Filters"
        />

        <VStack space="md" className="mt-4">
          <Text className="text-lg font-dm-sans-medium text-typography-700">
            Current Filter Values:
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 bg-background-50 p-4 rounded-lg">
            {JSON.stringify(filterValues, null, 2)}
          </Text>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default FilterComponentTest;
